import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YStack} from "tamagui";
import {usePostStore} from "@/stores/postStore";
import React, {useEffect, useState} from "react";
import MusicPlayer from "@/components/MusicPlayer/MusicPlayer";
import {useMusicPlayerStore} from "@/stores/useMusicPlayerStore";
import AudioRecorder from "@/components/AudioRecorder";
import {usePostSong} from "@/hooks/usePost"


const PostSongScreen = () => {
    const songToPost = usePostStore((state) => state.songToPost);
    const {unloadTrack} = useMusicPlayerStore();
    const [recordingUri, setRecordingUri] = useState<string | null>(null);
    const {mutate} = usePostSong();

    useEffect(() => {
        return () => {
            void unloadTrack();
        };
    }, []);

    //TODO: better Name
    const handlePost = () => {
        mutate({songData: songToPost, audioUri: recordingUri});
    };

    const handleAudioFileChange = (uri: string | null) => {
        setRecordingUri(uri);
    }

    return (
        <YStack
            flex={1}
            justifyContent="center"
            alignItems="center"
        >
            <MusicPlayer artworkURL={songToPost?.artworkURL.replace("{w}x{h}", "300x300")}
                         artistName={songToPost?.artistName} trackName={songToPost?.trackName}
                         audioPreviewURL={songToPost?.audioPreviewURL}/>
            {/*<Spacer size="$5" />*/}
            {/*<Input size="$4" borderWidth={2} width="73%"  placeholder="Add A Caption..." maxLength={50} borderColor='#fed900' />*/}
            <Spacer size="$5"/>
            <XStack>
                <AudioRecorder onFileChange={handleAudioFileChange}/>

            </XStack>
            <Button width="73%" size="$4" onPress={handlePost} backgroundColor='#fed900' color='black'>
                This is my Banger of the Day
            </Button>
        </YStack>
    );
};

export default PostSongScreen;