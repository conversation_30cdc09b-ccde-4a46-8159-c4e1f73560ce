import {authWithApple, authWithGoogle} from "@/api/authAPI";
import { api } from '@/api/apiConfig';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {router} from "expo-router";
import React, { createContext, useContext, useEffect, useState } from 'react';
import * as SecureStore from "expo-secure-store";

const USER_JWT_TOKEN = "user-token";
const USER_FIRST_NAME = "user-first-name";
const USER_LAST_NAME = "user-last-name";
const REFRESH_TOKEN = "refresh-token";

type AuthProps = {
    userJwtToken: string | null;
    isInitialSignIn: boolean | null;
    onAppleOAuth: (credentials: any) => Promise<void>;
    onGoogleOAuth: (credentials: any) => Promise<void>;
    onDemoAuth: () => Promise<void>;
    onLogout: () => Promise<void>;
    initialized: boolean;
}

const AuthContext = createContext<Partial<AuthProps>>({})

export function useAuth() {
    return useContext(AuthContext);
}

export const AuthProvider = ({children}: any) => {
    const [userJwtToken, setUserJWTToken] = useState<string | null>(null);
    const [initialized, setInitialized] = useState(false);
    const [isInitialSignIn, setIsInitialSignIn] = useState<boolean | null>(null);

    useEffect(() => {
        const loadUserJwtToken = async () => {
            try {
                const [storedUserJwtToken] = await Promise.all([
                    AsyncStorage.getItem(USER_JWT_TOKEN),
                ]);

                if (storedUserJwtToken) {
                    setUserJWTToken(storedUserJwtToken);
                    // Note: Authorization header is now handled by request interceptor
                }
            } catch (error) {
                console.error('Error loading auth tokens:', error);
            } finally {
                setInitialized(true);
            }
        };
        loadUserJwtToken();
    }, []);

    const storeAuthTokens = async (accessToken: string, refreshToken: string) => {
        try {
            await Promise.all([
                AsyncStorage.setItem(USER_JWT_TOKEN, accessToken),
                SecureStore.setItemAsync(REFRESH_TOKEN, refreshToken)
            ]);
            api.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
        } catch (error) {
            console.error('Error storing auth tokens:', error);
            throw error;
        }
    };

    const handleAppleOAuth = async (credentials: any) => {
        try {
            const response = await authWithApple(credentials);
            console.log("Apple Login Backend response:", response.userJwtToken);

            setUserJWTToken(response.userJwtToken);
            setIsInitialSignIn(response.isInitialSignIn);

            await Promise.all([
                storeAuthTokens(response.userJwtToken, response.userJwtRefreshToken),
                AsyncStorage.setItem(USER_FIRST_NAME, credentials.fullName.givenName),
                AsyncStorage.setItem(USER_LAST_NAME, credentials.fullName.familyName)
            ]);
        } catch (error: any) {
            throw {error: true, message: error};
        }
    }

    const handleGoogleOAuth = async (credentials: any) => {
        try {
            console.log("handling Google Login");
            const response = await authWithGoogle(credentials);
            console.log("Google Login Backend response:", response.userJwtToken);

            setUserJWTToken(response.userJwtToken);
            setIsInitialSignIn(response.isInitialSignIn);

            await storeAuthTokens(response.userJwtToken, response.userJwtRefreshToken);
        } catch (error: any) {
            throw {error: true, message: error};
        }
    }

    const handleDemoAuth = async () => {
        try {
            console.log("handling Demo Login");
            const DEMO_JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiZGFjNTIzNDAtNjM5Yy00YjE4LWI2NzktODk5YTFmZDViYzcxIiwiZXhwaXJlcyI6MTczNDA3NzkzMi4xNTIxNzU0fQ.ppnsqUI8ITTdGg0n4k4IwR1NGcAlYlll2LpuRTVTdek';
            const DEMO_REFRESH_TOKEN = 'demo-refresh-token'; // Add a demo refresh token if needed

            setUserJWTToken(DEMO_JWT_TOKEN);
            setIsInitialSignIn(false);

            await storeAuthTokens(DEMO_JWT_TOKEN, DEMO_REFRESH_TOKEN);
        } catch (error: any) {
            throw {error: true, message: error.data};
        }
    }

    const handleLogout = async () => {
        console.log("Logout");
        setUserJWTToken(null);
        setIsInitialSignIn(null);

        await Promise.all([
            AsyncStorage.removeItem(USER_JWT_TOKEN),
            AsyncStorage.removeItem(USER_FIRST_NAME),
            AsyncStorage.removeItem(USER_LAST_NAME),
            SecureStore.deleteItemAsync(REFRESH_TOKEN)
        ]);

        api.defaults.headers.common['Authorization'] = '';
        router.replace("/login");
    }

    const value = {
        initialized,
        onGoogleOAuth: handleGoogleOAuth,
        onAppleOAuth: handleAppleOAuth,
        onDemoAuth: handleDemoAuth,
        onLogout: handleLogout,
        userJwtToken,
        isInitialSignIn
    }

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    )
}