export interface AppleAuthRequets {
  authorizationCode: string;
  email: string | null;
  fullName: {
    familyName: string | null;
    givenName: string | null;
    middleName: string | null;
    namePrefix: string | null;
    nameSuffix: string | null;
    nickname: string | null;
  };
  identityToken: string;
  realUserStatus: number;
  state: string | null;
  user: string;
}

export interface AppleAuthResponse {
  userJwtToken: string;
  userJwtRefreshToken: string;
  isInitialSignIn: boolean;
  fullName: {
    familyName: string ;
    givenName: string ;
  };
}


export interface GoogleAuthRequest {
  idToken: string;
  scopes: string[];
  serverAuthCode: null;
  user: {
    email: string;
    familyName: string;
    givenName: string;
    id: string;
    name: string;
    photo: string;
  };
}

export interface GoogleAuthResponse {
  userJwtRefreshToken: string;
  userJwtToken: string;
  isInitialSignIn: boolean;
}

export interface RefreshTokenResponse {
  userJwtToken: string;
  userJwtRefreshToken: string;
}
