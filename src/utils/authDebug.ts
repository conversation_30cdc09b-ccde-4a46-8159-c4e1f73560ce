import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { getAuthDebugInfo, validateAndRefreshToken, isTokenExpired, isValidJWTFormat } from '@/api/apiConfig';

/**
 * Comprehensive authentication debugging utility
 * Use this to diagnose authentication issues
 */
export class AuthDebugger {
    
    /**
     * Get comprehensive authentication status
     */
    static async getFullAuthStatus() {
        console.log('=== AUTH DEBUG: Full Status Check ===');
        
        try {
            // Get basic debug info
            const debugInfo = await getAuthDebugInfo();
            console.log('Basic Auth Info:', debugInfo);
            
            // Get stored tokens directly
            const [accessToken, refreshToken] = await Promise.all([
                AsyncStorage.getItem('user-token'),
                SecureStore.getItemAsync('refresh-token')
            ]);
            
            console.log('Stored Access Token:', accessToken ? 'Present' : 'Missing');
            console.log('Stored Refresh Token:', refreshToken ? 'Present' : 'Missing');
            
            if (accessToken) {
                console.log('Access Token Valid Format:', isValidJWTFormat(accessToken));
                console.log('Access Token Expired:', isTokenExpired(accessToken));
                
                // Try to decode token payload for more info
                try {
                    const payload = JSON.parse(atob(accessToken.split('.')[1]));
                    console.log('Token Payload:', {
                        userId: payload.user_id,
                        expires: payload.expires,
                        expiresDate: new Date(payload.expires * 1000),
                        currentTime: new Date(),
                        timeUntilExpiry: payload.expires ? (payload.expires * 1000 - Date.now()) / 1000 / 60 : 'N/A'
                    });
                } catch (e) {
                    console.log('Could not decode token payload:', e);
                }
            }
            
            return {
                debugInfo,
                hasAccessToken: !!accessToken,
                hasRefreshToken: !!refreshToken,
                accessTokenValid: accessToken ? isValidJWTFormat(accessToken) : false,
                accessTokenExpired: accessToken ? isTokenExpired(accessToken) : null
            };
            
        } catch (error) {
            console.error('Error getting auth status:', error);
            return null;
        }
    }
    
    /**
     * Test token validation and refresh flow
     */
    static async testTokenValidation() {
        console.log('=== AUTH DEBUG: Testing Token Validation ===');
        
        try {
            const result = await validateAndRefreshToken();
            console.log('Token validation result:', result ? 'Success' : 'Failed');
            
            if (result) {
                console.log('Valid token obtained:', result.substring(0, 20) + '...');
            }
            
            return !!result;
        } catch (error) {
            console.error('Token validation test failed:', error);
            return false;
        }
    }
    
    /**
     * Clear all authentication data (for testing)
     */
    static async clearAllAuthData() {
        console.log('=== AUTH DEBUG: Clearing All Auth Data ===');
        
        try {
            await Promise.all([
                AsyncStorage.removeItem('user-token'),
                AsyncStorage.removeItem('user-first-name'),
                AsyncStorage.removeItem('user-last-name'),
                AsyncStorage.removeItem('is-initial-sign-in'),
                SecureStore.deleteItemAsync('refresh-token')
            ]);
            
            console.log('All auth data cleared');
            return true;
        } catch (error) {
            console.error('Error clearing auth data:', error);
            return false;
        }
    }
    
    /**
     * Simulate token expiration (for testing)
     */
    static async simulateTokenExpiration() {
        console.log('=== AUTH DEBUG: Simulating Token Expiration ===');
        
        try {
            // Create an expired token for testing
            const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoidGVzdCIsImV4cCI6MTAwMDAwMDAwMH0.test';
            await AsyncStorage.setItem('user-token', expiredToken);
            
            console.log('Expired token set for testing');
            return true;
        } catch (error) {
            console.error('Error simulating token expiration:', error);
            return false;
        }
    }
    
    /**
     * Run all debug tests
     */
    static async runAllTests() {
        console.log('=== AUTH DEBUG: Running All Tests ===');
        
        const results = {
            fullStatus: await this.getFullAuthStatus(),
            tokenValidation: await this.testTokenValidation()
        };
        
        console.log('=== AUTH DEBUG: Test Results ===');
        console.log(JSON.stringify(results, null, 2));
        
        return results;
    }
}

/**
 * Quick debug function that can be called from anywhere
 */
export const debugAuth = () => AuthDebugger.runAllTests();

/**
 * Export individual methods for convenience
 */
export const {
    getFullAuthStatus,
    testTokenValidation,
    clearAllAuthData,
    simulateTokenExpiration,
    runAllTests
} = AuthDebugger;
