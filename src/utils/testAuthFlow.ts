import { api } from '@/api/apiConfig';
import { AuthDebugger } from '@/utils/authDebug';

/**
 * Utility functions to test the authentication flow
 */
export class AuthFlowTester {
    
    /**
     * Test the complete authentication flow
     */
    static async testCompleteFlow() {
        console.log('=== TESTING COMPLETE AUTH FLOW ===');
        
        try {
            // 1. Get initial auth status
            console.log('1. Getting initial auth status...');
            await AuthDebugger.getFullAuthStatus();
            
            // 2. Test a simple API call
            console.log('2. Testing API call...');
            const response = await api.get('/v1/test-endpoint');
            console.log('API call successful:', response.status);
            
            return true;
        } catch (error: any) {
            console.error('Auth flow test failed:', error.response?.data || error.message);
            return false;
        }
    }
    
    /**
     * Test token refresh by making an API call with an expired token
     */
    static async testTokenRefresh() {
        console.log('=== TESTING TOKEN REFRESH ===');
        
        try {
            // 1. Simulate expired token scenario
            console.log('1. Simulating expired token...');
            
            // 2. Make an API call that should trigger refresh
            console.log('2. Making API call to trigger refresh...');
            const response = await api.get('/v1/user/profile');
            console.log('API call after refresh successful:', response.status);
            
            return true;
        } catch (error: any) {
            console.error('Token refresh test failed:', error.response?.data || error.message);
            return false;
        }
    }
    
    /**
     * Test authentication failure handling
     */
    static async testAuthFailureHandling() {
        console.log('=== TESTING AUTH FAILURE HANDLING ===');
        
        try {
            // 1. Clear all tokens to simulate auth failure
            console.log('1. Clearing all auth data...');
            await AuthDebugger.clearAllAuthData();
            
            // 2. Try to make an API call that requires authentication
            console.log('2. Making API call without auth...');
            const response = await api.get('/v1/user/profile');
            console.log('Unexpected success:', response.status);
            
            return false; // Should not reach here
        } catch (error: any) {
            console.log('Expected auth failure occurred:', error.response?.status);
            return error.response?.status === 401;
        }
    }
    
    /**
     * Run all authentication tests
     */
    static async runAllTests() {
        console.log('🧪 STARTING COMPREHENSIVE AUTH TESTS 🧪');
        
        const results = {
            completeFlow: false,
            tokenRefresh: false,
            authFailure: false
        };
        
        try {
            results.completeFlow = await this.testCompleteFlow();
            results.tokenRefresh = await this.testTokenRefresh();
            results.authFailure = await this.testAuthFailureHandling();
            
            console.log('📊 TEST RESULTS:', results);
            
            const allPassed = Object.values(results).every(result => result);
            console.log(allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
            
            return results;
        } catch (error) {
            console.error('Error running auth tests:', error);
            return results;
        }
    }
}
