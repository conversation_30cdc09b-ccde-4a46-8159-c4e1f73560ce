import axios, { AxiosError } from 'axios';
import Constants from 'expo-constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import {router} from "expo-router";
import {refreshAuthToken} from "@/api/authAPI";

// Constants for storage keys
const USER_JWT_TOKEN = "user-token";
const REFRESH_TOKEN = "refresh-token";

let baseURL = 'https://api.wullup.com/api';

// Global state for token refresh
let isRefreshing = false;
let failedQueue: Array<{
    resolve: (value?: any) => void;
    reject: (reason?: any) => void;
}> = [];

const getDevURL = () => {
    const debuggerHost = Constants.manifest2?.extra?.expoGo?.debuggerHost;
    if (debuggerHost) {
        const host = debuggerHost.split(':').shift();
        return `http://${host}:8001`;
    }
};


console.log("NODE_ENV", process.env.NODE_ENV);
console.log(getDevURL())

if (process.env.NODE_ENV === "development") {
    baseURL = getDevURL() + '/api';
}

export const api = axios.create({
    baseURL,
    headers: {
        'Content-Type': 'application/json',
    },
});

// Helper function to process failed queue
const processQueue = (error: any, token: string | null = null) => {
    failedQueue.forEach(({ resolve, reject }) => {
        if (error) {
            reject(error);
        } else {
            resolve(token);
        }
    });

    failedQueue = [];
};

// Request interceptor to add Authorization header dynamically
api.interceptors.request.use(
    async (config) => {
        // Skip adding auth header for auth endpoints
        if (config.url?.includes('/auth/')) {
            return config;
        }

        try {
            const token = await AsyncStorage.getItem(USER_JWT_TOKEN);
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
            }
        } catch (error) {
            console.error('Error getting token for request:', error);
        }

        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Response interceptor with improved token refresh logic
api.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
        const originalRequest = error.config;

        if (error.response?.status === 403 && originalRequest && !(originalRequest as any)._retry) {
            if (isRefreshing) {
                // If refresh is already in progress, queue this request
                return new Promise((resolve, reject) => {
                    failedQueue.push({ resolve, reject });
                }).then(() => {
                    // Retry the original request with fresh token
                    return api.request(originalRequest);
                }).catch(err => {
                    return Promise.reject(err);
                });
            }

            (originalRequest as any)._retry = true;
            isRefreshing = true;

            try {
                const refreshToken = await SecureStore.getItemAsync(REFRESH_TOKEN);
                const userJwtToken = await AsyncStorage.getItem(USER_JWT_TOKEN);

                if (!refreshToken || !userJwtToken) {
                    throw new Error('No refresh token found');
                }

                console.log('Refreshing token...');
                const response = await refreshAuthToken(refreshToken, userJwtToken);

                // Store new tokens
                await Promise.all([
                    AsyncStorage.setItem(USER_JWT_TOKEN, response.userJwtToken),
                    SecureStore.setItemAsync(REFRESH_TOKEN, response.userJwtRefreshToken)
                ]);

                console.log('Token refreshed successfully');

                // Process the queue with success
                processQueue(null, response.userJwtToken);

                // Update the original request with new token
                originalRequest.headers.Authorization = `Bearer ${response.userJwtToken}`;

                return api.request(originalRequest);
            } catch (refreshError) {
                console.error('Token refresh failed:', refreshError);

                // Process the queue with error
                processQueue(refreshError, null);

                // Clear tokens and redirect to login
                await Promise.all([
                    AsyncStorage.removeItem(USER_JWT_TOKEN),
                    SecureStore.deleteItemAsync(REFRESH_TOKEN).catch(() => {})
                ]);

                router.replace('/login');
                return Promise.reject(refreshError);
            } finally {
                isRefreshing = false;
            }
        }

        return Promise.reject(error);
    }
);